{% extends "be/include/base.html" %}

{% set page = 'WARRANTY' %}
{% set title = curWarranty is empty ? 'Nuova Garanzia' : 'Modifica Garanzia' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/warranty.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_WARRANTY', '{{ routes("BE_WARRANTY") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curWarranty is empty %}
            <h5 class="mb-0">Inserisci Garanzia</h5>
            {% else %}
            <h5 class="mb-0">Modifica Garanzia</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_WARRANTY_SAVE') %}
            {% if curWarranty.id is not empty %}                
            {% set postUrl = routes('BE_WARRANTY_SAVE') + '?warrantyId=' + curWarranty.id %}
            {% endif %}

            <form id="warranty-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="code" type="text" class="form-control form-control-maxlength" placeholder="Codice identificativo" value="{{ curWarranty.code }}" required maxlength="50">
                        <div class="form-text text-muted">Codice univoco per identificare la garanzia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Titolo: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="title" type="text" class="form-control form-control-maxlength" placeholder="Titolo della garanzia" value="{{ curWarranty.title }}" required maxlength="200">
                        <div class="form-text text-muted">Titolo descrittivo della garanzia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Descrizione:</label>
                    <div class="col-lg-9">
                        <textarea name="description" class="form-control form-control-maxlength" rows="3" placeholder="Descrizione dettagliata" maxlength="1000">{{ curWarranty.description }}</textarea>
                        <div class="form-text text-muted">Descrizione dettagliata della garanzia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipo di Garanzia: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select name="warrantyTypeId" class="form-control" data-placeholder="Seleziona tipo di garanzia" required>
                            <option value=""></option>
                            {% set warrantyTypes = lookup('WarrantyType', checkPublished=false, language='false') %}
                            {% if warrantyTypes is not empty %}
                                {% for warrantyType in warrantyTypes %}
                                    <option value="{{ warrantyType.id }}" {{ curWarranty.warrantyTypeId equals warrantyType.id ? 'selected' : '' }}>{{ warrantyType.name }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona il tipo di garanzia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Compagnia Assicurativa: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select name="insuranceCompanyId" class="form-control" data-placeholder="Seleziona compagnia assicurativa" required>
                            <option value=""></option>
                            {% set insuranceCompanies = lookup('InsuranceCompany', checkPublished=false, language='false') %}
                            {% if insuranceCompanies is not empty %}
                                {% for insuranceCompany in insuranceCompanies %}
                                    <option value="{{ insuranceCompany.id }}" {{ curWarranty.insuranceCompanyId equals insuranceCompany.id ? 'selected' : '' }}>{{ insuranceCompany.description }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona la compagnia assicurativa.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Campi Criteri:</label>
                    <div class="col-lg-9">
                        <select name="criteriaFields" class="form-control select" multiple data-placeholder="Seleziona tipo di criterio">
                            {% for criteriaType in criteriaTypes %}
                                <option value="{{ criteriaType.code }}" {{ curWarranty.criteriaFields is not empty and curWarranty.criteriaFields contains criteriaType.code ? 'selected' : '' }}>{{ criteriaType.label }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text text-muted">Seleziona il tipo di criterio.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_WARRANTY_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curWarranty is empty %}
                        Crea Garanzia
                        {% else %}
                        Aggiorna Garanzia
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
