package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.BasePojo;

/**
 *
 * <AUTHOR>
 */
public class GetFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("id");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        ObjectId id = (ObjectId) args.get("id");
        
        if (StringUtils.isBlank(table)) {
            logger.error("missing table");
            return null;
        }
        if (id == null) {
            logger.error("missing id");
            return null;
        }
        
        return get(table, id);
    }
    
    private Object get(String table, ObjectId id) {
        Object obj = null;
        
        final String packageName = StringUtils.substringBeforeLast((new BasePojo()).getClass().getCanonicalName(), ".");
        String className = packageName + "." + StringUtils.capitalize(table);
        
        Class clss = null;
        try {
            clss = Class.forName(className);
        } catch (Exception ignored) {
            // ignored
        }
        if (clss == null) {
            throw new InvalidParameterException("missing class " + className + " for table " + table);
        }
        
        try {
            obj = BaseDao.getDocumentById(id, clss);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        
        return obj;
    }
    
}
