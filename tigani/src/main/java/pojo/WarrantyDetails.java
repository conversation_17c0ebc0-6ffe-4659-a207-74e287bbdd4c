package pojo;

import org.bson.types.ObjectId;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class WarrantyDetails extends BasePojo {

    private ObjectId warrantyId;
    private List<String> provinceCode;
    private Integer claimNumber;
    private ObjectId insuranceProvenanceTypeId;
    private Integer universalClass;
    private Double premiumValue;

    public ObjectId getWarrantyId() {
        return warrantyId;
    }

    public void setWarrantyId(ObjectId warrantyId) {
        this.warrantyId = warrantyId;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public Integer getClaimNumber() {
        return claimNumber;
    }

    public void setClaimNumber(Integer claimNumber) {
        this.claimNumber = claimNumber;
    }

    public ObjectId getInsuranceProvenanceTypeId() {
        return insuranceProvenanceTypeId;
    }

    public void setInsuranceProvenanceTypeId(ObjectId insuranceProvenanceTypeId) {
        this.insuranceProvenanceTypeId = insuranceProvenanceTypeId;
    }

    public Integer getUniversalClass() {
        return universalClass;
    }

    public void setUniversalClass(Integer universalClass) {
        this.universalClass = universalClass;
    }

    public Double getPremiumValue() {
        return premiumValue;
    }

    public void setPremiumValue(Double premiumValue) {
        this.premiumValue = premiumValue;
    }
}
