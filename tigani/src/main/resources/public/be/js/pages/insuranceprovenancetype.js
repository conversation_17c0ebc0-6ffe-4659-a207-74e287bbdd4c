const InsuranceProvenanceTypeSettings = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
    };

    // Validation
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Unstyled checkboxes, radios
                if (element.parents().hasClass('form-check')) {
                    error.appendTo(element.parents('.form-check').parent());
                }

                // Input with icons and Select2
                else if (element.parents().hasClass('form-group-feedback') || element.hasClass('select2-hidden-accessible')) {
                    error.appendTo(element.parent());
                }

                // Input group, styled file input
                else if (element.parent().is('.uniform-uploader, .uniform-select') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            },
            rules: {
                code: {
                    required: true,
                    minlength: 1
                },
                title: {
                    required: true,
                    minlength: 3
                }
            },
            messages: {
                code: {
                    required: 'Il codice è obbligatorio',
                    minlength: 'Il codice deve essere di almeno 2 caratteri'
                },
                title: {
                    required: 'Il titolo è obbligatorio',
                    minlength: 'Il titolo deve essere di almeno 3 caratteri'
                }
            }
        });
    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.form-control-maxlength').maxlength({
            alwaysShow: true,
            threshold: 10,
            warningClass: 'badge bg-warning text-white position-absolute top-0 end-0 mt-1 me-1',
            limitReachedClass: 'badge bg-danger text-white position-absolute top-0 end-0 mt-1 me-1'
        });
    };
    
    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------
document.addEventListener('DOMContentLoaded', function () {
    InsuranceProvenanceTypeSettings.init();  
    submitInsuranceProvenanceType();
});

function submitInsuranceProvenanceType() {
    // Form submission handling
    $('#insuranceprovenancetype-edit').submit(function (e) {
        if ($('#insuranceprovenancetype-edit').valid()) {
            e.preventDefault();
            const formData = new FormData(this);

            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_INSURANCEPROVENANCETYPE") + "?insuranceProvenanceTypeId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during insurance provenance type save/update', error);
                }
            });
        }
    });
}
