// Global variable to store the CKEditor instance
let descriptionEditor;

const Mailtemplate = function () {
    // Initialization of components
    const init = function () {
        _componentSelect2();
        _componentCKEditorClassic();
        _componentDaterange();
        _componentFilePond();
        _componentValidate();
        _componentMaxlength();
    };

    // CKEditor
    const _componentCKEditorClassic = function () {
        if (typeof ClassicEditor === 'undefined') {
            console.warn('Warning - ckeditor_classic.js is not loaded.');
            return;
        }

        // Editor with placeholder
        ClassicEditor.create(document.querySelector('#descriptionn'), {
            language: 'it',
            toolbar: {
                //shouldNotGroupWhenFull: true
            },
            htmlSupport: {
                allow: [
                    {
                        name: /.*/,
                        attributes: true,
                        classes: true,
                        styles: true
                    }
                ]
            },
            heading: {
                options: [
                    {model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph'},
                    //{model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1'}, // H1 is the title
                    {model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2'},
                    {model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3'},
                    {model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4'},
                    {model: 'heading5', view: 'h5', title: 'Heading 5', class: 'ck-heading_heading5'},
                    {model: 'heading6', view: 'h6', title: 'Heading 6', class: 'ck-heading_heading6'}
                ]
            },
            simpleUpload: {
                uploadUrl: appRoutes.get("BE_IMAGE_SAVE")
            }
        }).then(editor => {
            // Store the editor instance globally so we can access it later
            descriptionEditor = editor;
        }).catch(error => {
            console.error(error);
        });

    };

    // Select2
    const _componentSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Default initialization
        $('.select').select2({
            language: "it"
        });


        // Format icon
        function iconFormat(icon) {
            if (!icon.id) {
                return icon.text;
            }
            var $icon = '<i class="ph-' + $(icon.element).data('icon') + '"></i>' + icon.text;

            return $icon;
        }

        // Initialize with options
        $('.select-icons').select2({
            templateResult: iconFormat,
            minimumResultsForSearch: Infinity,
            templateSelection: iconFormat,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // Format country
        function formatLanguage(state) {
            if (!state.id) {
                return state.text;
            }
            var baseUrl = pageVariables.get("contextPath") + '/be/images/lang/';
            var $state = $(
                    '<span><img src="' + baseUrl + '/' + state.element.value.toLowerCase() + '.svg" class="img-flag" /> ' + state.text + '</span>');
            return $state;
        }
        ;

        $(".select-language").select2({
            templateResult: formatLanguage,
            templateSelection: formatLanguage
        });

    };

    // Daterange picker
    const _componentDaterange = function () {
        if (!$().daterangepicker) {
            console.warn('Warning - daterangepicker.js is not loaded.');
            return;
        }

        // Single picker
        $('.daterange-single').daterangepicker({
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            drops: 'up',
            startDate: $('.daterange-single').val() || moment().format('DD/MM/YYYY'),
            parentEl: '.content-inner',
            singleDatePicker: true,
            autoApply: true
        });

    };

    // FilePond
    const _componentFilePond = function () {

        // Social share preview

        FilePond.registerPlugin(
                FilePondPluginImageExifOrientation,
                FilePondPluginImagePreview,
                FilePondPluginImageCrop,
                FilePondPluginImageResize,
                FilePondPluginImageFilter,
                FilePondPluginImageTransform,
                FilePondPluginImageEdit,
                FilePondPluginImageValidateSize,
                FilePondPluginFileEncode,
                FilePondPluginFileValidateType,
                FilePondPluginFileValidateSize
                );
        FilePond.setOptions(FilePondIT);
        const inputElement = document.querySelector('input[type="file"]');

        const doka = dokaCreate({
            //utils: 'crop, color',
            cropAspectRatioOptions: [
                {
                    label: 'Anteprima social',
                    value: 630 / 1200
                }
            ],
            crop: {
                aspectRatio: 630 / 1200
            },
            labelButtonReset: "Reimposta",
            labelButtonCancel: "Annulla",
            labelButtonConfirm: "Conferma",
            labelButtonUtilCrop: "Ritaglia",
            labelButtonUtilResize: "Ridimensiona",
            labelButtonUtilFilter: "Filtra",
            labelButtonUtilColor: "Colori",
            labelButtonUtilMarkup: "Annota",
            labelStatusMissingWebGL: "WebGL è richiesto ma è disabilitato nel tuo browser",
            labelStatusAwaitingImage: "In attesa dell'immagine…",
            labelStatusLoadImageError: "Errore nel caricamento dell'immagine…",
            labelStatusLoadingImage: "Caricamento dell'immagine…",
            labelStatusProcessingImage: "Elaborazione dell'immagine…",
            labelColorBrightness: "Luminosità",
            labelColorContrast: "Contrasto",
            labelColorExposure: "Esposizione",
            labelColorSaturation: "Saturazione",
            labelMarkupTypeRectangle: "Rettangolo",
            labelMarkupTypeEllipse: "Cerchio",
            labelMarkupTypeText: "Testo",
            labelMarkupTypeLine: "Linea",
            labelMarkupSelectFontSize: "Dimensione",
            labelMarkupSelectFontFamily: "Carattere",
            labelMarkupSelectLineDecoration: "Decorazione",
            labelMarkupSelectLineStyle: "Stile",
            labelMarkupSelectShapeStyle: "Stile",
            labelMarkupRemoveShape: "Rimuovi",
            labelMarkupToolSelect: "Seleziona",
            labelMarkupToolDraw: "Disegna",
            labelMarkupToolLine: "Linea",
            labelMarkupToolText: "Testo",
            labelMarkupToolRect: "Rettangolo",
            labelMarkupToolEllipse: "Cerchio",
            labelResizeWidth: "Larghezza",
            labelResizeHeight: "Altezza",
            labelResizeApplyChanges: "Applica modifiche",
            labelCropInstructionZoom: "Zoom avanti e indietro con la rotellina del mouse o il touchpad.",
            labelButtonCropZoom: "Zoom",
            labelButtonCropRotateLeft: "Ruota a sinistra",
            labelButtonCropRotateRight: "Ruota a destra",
            labelButtonCropRotateCenter: "Centra rotazione",
            labelButtonCropFlipHorizontal: "Rifletti orizzontalmente",
            labelButtonCropFlipVertical: "Rifletti verticalmente",
            labelButtonCropAspectRatio: "Proporzioni",
            labelButtonCropToggleLimit: "Selezione ritaglio",
            labelButtonCropToggleLimitEnable: "Limitato all'immagine",
            labelButtonCropToggleLimitDisable: "Seleziona fuori immagine",
            pointerEventsPolyfillScope: "ambito",
            styleCropCorner: "angolo",
            styleFullscreenSafeArea: "area sicura a schermo intero"

        });

        pond = FilePond.create(inputElement, {

            allowFileEncode: true,
            allowImagePreview: true,
            allowImageTransform: true,
            allowImageEdit: true,
            allowImageCrop: true,
            allowImageResize: true,
            allowImageExifOrientation: true,
            maxFileSize: '5MB',
            acceptedFileTypes: ['image/png', 'image/jpeg', 'image/svg'],
            imageEditEditor: doka
        });

        doka.on('confirm', (output) => {
            console.log(output);
        });

        // Load initial image if present
        var imageIds = pageVariables.get("imageIds");
        if (typeof imageIds !== "undefined" && imageIds) {
            var images = imageIds.replace("[", "").replace("]", "");
            if (images.includes(",")) {
                var imagesToLoad = images.split(", ");
                imagesToLoad.forEach(function (element) {
                    var image = appRoutes.get("BE_IMAGE") + "?oid=" + element;
                    pond.addFile(image);
                });
            } else {
                var image = appRoutes.get("BE_IMAGE") + "?oid=" + imageIds.replace("[", "").replace("]", "");
                pond.addFile(image);
            }
        }
    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // custom url validation
        $.validator.addMethod('identifier', function (value) {
            return /^[a-z0-9-_]+$/.test(value);
        }, 'URL non valido. L\'identificatore deve contenere solo lettere minuscole, numeri, trattini e sottolineature.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------
document.addEventListener('DOMContentLoaded', function () {
    Mailtemplate.init();

    updateIdentifier();
    submitMailtemplate();

});

function submitMailtemplate() {
    // Form submission handling
    $('#mailtemplate').submit(function (e) {
        if ($('#mailtemplate').valid()) {
            e.preventDefault();
            const formData = new FormData(this);

            // Manually add CKEditor content to FormData
            if (descriptionEditor) {
                const editorContent = descriptionEditor.getData();
                formData.set('description', editorContent);
            }

            if (pageVariables.has("parentId")) {
                formData.append('parentId', pageVariables.get("parentId"));
                formData.append('parentIdLanguage', pageVariables.get("parentIdLanguage"));
            }
            if (pageVariables.has("language")) {
                formData.append('language', pageVariables.get("language"));
            }
            // formData.append('language', "en");
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_MAILTEMPLATE") + "?mailtemplateId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during mailtemplate save/update', error);
                }
            });
        }
    });
}

function changeMailtemplateLanguage(parentId, language) {
    var newUrl = location.protocol + '//' + location.host + location.pathname;
    newUrl += "?parentId=" + parentId + "&language=" + language;

    window.location.href = newUrl;
}

function createMailtemplateLanguage(parentId, parentIdLanguage, language) {
    var newUrl = location.protocol + '//' + location.host + location.pathname;
    newUrl += "?parentId=" + parentId + "&parentIdLanguage=" + parentIdLanguage + "&language=" + language;

    window.location.href = newUrl;
}

function updateIdentifier() {
    // Event listener for changes in the 'title' field
    if ($("#title").length > 0) {
        document.getElementById('title').addEventListener('input', function () {
            if (!pageVariables.get("mailtemplateId")) {
                // Only update the 'identifier' if it is currently empty
                var identifierInput = document.getElementById('identifier');
                var slug = slugify(this.value);
                identifierInput.value = slug;
                updateIdentifierPreview(slug); // Update preview directly with slug
            }
        });

        // Event listener for changes in the 'identifier' field
        document.getElementById('identifier').addEventListener('input', function () {
            updateIdentifierPreview(this.value); // Directly use the new value for preview
        });
    }
}

// Funzione ausiliaria per convertire base64 in Blob
function base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
    }

    const byteArray = new Uint8Array(byteArrays);
    return new Blob([byteArray], {type: mimeType});
}

// Utility function to 'slugify' the text
function slugify(text) {
    return text
        .toString()                             // Converti in stringa
        .normalize('NFD')                       // Normalizza la stringa con decomposizione canonica
        .replace(/[\u0300-\u036f]/g, '')        // Rimuovi i segni diacritici (accenti)
        .toLowerCase()                          // Converte in minuscolo
        .trim()                                 // Rimuovi spazi bianchi all'inizio e alla fine
        .replace(/\s+/g, '-')                   // Sostituisci gli spazi con trattini
        .replace(/[^\w\-]+/g, '')               // Rimuovi caratteri non alfanumerici e trattini
        .replace(/\-\-+/g, '-');                // Sostituisci più trattini con un singolo trattino
}

// Utility function to update the 'identifierPreview'
function updateIdentifierPreview(slug) {
    var identifierPreview = document.getElementById('identifier-preview');
    if (identifierPreview) {
        identifierPreview.innerText = slug;
    }
}