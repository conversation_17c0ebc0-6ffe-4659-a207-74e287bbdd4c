const ChannelSettings = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
    };

    // Select2
    const _componentSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Default initialization
        $('.select').select2({
            language: "it"
        });


        // Format icon
        function iconFormat(icon) {
            if (!icon.id) {
                return icon.text;
            }
            var $icon = '<i class="ph-' + $(icon.element).data('icon') + '"></i>' + icon.text;

            return $icon;
        }

        // Initialize with options
        $('.select-icons').select2({
            templateResult: iconFormat,
            minimumResultsForSearch: Infinity,
            templateSelection: iconFormat,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // Format country
        function formatLanguage(state) {
            if (!state.id) {
                return state.text;
            }
            var baseUrl = pageVariables.get("contextPath") + '/be/images/lang/';
            var $state = $(
                    '<span><img src="' + baseUrl + '/' + state.element.value.toLowerCase() + '.svg" class="img-flag" /> ' + state.text + '</span>');
            return $state;
        }
        ;

        $(".select-language").select2({
            templateResult: formatLanguage,
            templateSelection: formatLanguage
        });

    };

    // Validation
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function(element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            success: function(label) {
                label.addClass('validation-valid-label').text('Success.'); // remove to hide Success message
            },

            // Different components require proper error label placement
            errorPlacement: function(error, element) {

                // Unstyled checkboxes, radios
                if (element.parents().hasClass('form-check')) {
                    error.appendTo( element.parents('.form-check').parent() );
                }

                // Input with icons and Select2
                else if (element.parents().hasClass('form-group-feedback') || element.hasClass('select2-hidden-accessible')) {
                    error.appendTo( element.parent() );
                }

                // Input group, styled file input
                else if (element.parent().is('.uniform-uploader, .uniform-select') || element.parents().hasClass('input-group')) {
                    error.appendTo( element.parent().parent() );
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            },
            rules: {
                code: {
                    required: true,
                    maxlength: 50
                },
                capacityLimit: {
                    min: 0
                }
            }
        });

        // Reset form
        $('#reset').on('click', function() {
            validator.resetForm();
        });
    };

    // Maxlength
    const _componentMaxlength = function() {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.form-control-maxlength').maxlength({
            alwaysShow: true,
            threshold: 10,
            warningClass: "badge bg-warning",
            limitReachedClass: "badge bg-danger"
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
document.addEventListener('DOMContentLoaded', function () {
    ChannelSettings.init();
    submitChannel();
});

function submitChannel() {
    // Form submission handling
    $('#channel-edit').submit(function (e) {
        if ($('#channel-edit').valid()) {
            e.preventDefault();
            const formData = new FormData(this);

            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_CHANNEL") + "?channelId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during channel save/update', error);
                }
            });
        }
    });
}
