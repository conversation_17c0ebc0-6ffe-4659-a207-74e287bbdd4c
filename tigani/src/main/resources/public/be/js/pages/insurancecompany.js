const InsuranceCompanySettings = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDropUploader();
    };

    // Validation
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Unstyled checkboxes, radios
                if (element.parents().hasClass('form-check')) {
                    error.appendTo(element.parents('.form-check').parent());
                }

                // Input with icons and Select2
                else if (element.parents().hasClass('form-group-feedback') || element.hasClass('select2-hidden-accessible')) {
                    error.appendTo(element.parent());
                }

                // Input group, styled file input
                else if (element.parent().is('.uniform-uploader, .uniform-select') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            },
            rules: {
                code: {
                    required: true,
                    minlength: 2
                },
                description: {
                    required: true,
                    minlength: 3
                },
                endpoint: {
                    url: true
                }
            },
            messages: {
                code: {
                    required: 'Il codice è obbligatorio',
                    minlength: 'Il codice deve essere di almeno 2 caratteri'
                },
                description: {
                    required: 'La descrizione è obbligatoria',
                    minlength: 'La descrizione deve essere di almeno 3 caratteri'
                },
                endpoint: {
                    url: 'Inserisci un URL valido'
                }
            }
        });
    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.form-control-maxlength').maxlength({
            alwaysShow: true,
            threshold: 10,
            warningClass: 'badge bg-warning text-white position-absolute top-0 end-0 mt-1 me-1',
            limitReachedClass: 'badge bg-danger text-white position-absolute top-0 end-0 mt-1 me-1'
        });
    };
    
    // Drop Uploader
    // ------------------------------
    const _componentDropUploader = function () {
        $('input[attachment=true]').drop_uploader({
            uploader_text: $('#uploader-text').text(),
            browse_text: 'browse',
            browse_css_class: '',
            browse_css_selector: 'file_browse',
            uploader_icon: '<i class="fa fa-upload fa-2x"></i>',
            file_icon: '<i class="fa fa-file-o"></i>',
            time_show_errors: 5,
            layout: 'list',
            method: 'normal',
            url: '',
            delete_url: ''
        });
    };
    
    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------
document.addEventListener('DOMContentLoaded', function () {
    InsuranceCompanySettings.init();  
    submitInsuranceCompany();
});

function submitInsuranceCompany() {
    // Form submission handling
    $('#insurancecompany-edit').submit(function (e) {
        if ($('#insurancecompany-edit').valid()) {
            e.preventDefault();
            const formData = new FormData(this);

            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_INSURANCECOMPANY") + "?insuranceCompanyId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during insurance company save/update', error);
                }
            });
        }
    });
}
